#!/usr/bin/env python3
"""
Simple DeepSeek Browser-Use Agent Example

This script demonstrates the most basic browser-use functionality with DeepSeek:
- Create an agent with DeepSeek model
- Perform a simple task
- Get results

Perfect for first-time users to understand how browser-use works with DeepSeek.
"""

import asyncio
import os
from dotenv import load_dotenv
from browser_use import Agent
from browser_use.llm import ChatDeepSeek

# Load environment variables
load_dotenv()

async def main():
    # Get DeepSeek API key from environment variables
    deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
    if deepseek_api_key is None:
        print('DEEPSEEK_API_KEY not found in environment variables.')
        print('Please set it in your .env file:')
        print('DEEPSEEK_API_KEY=your_actual_api_key_here')
        return

    # Initialize the DeepSeek model
    llm = ChatDeepSeek(
        model='deepseek-chat',
        api_key=deepseek_api_key
    )

    # Define a simple task
    task = "Search Google for 'what is browser automation' and tell me the top 3 results"

    # Create and run the agent
    agent = Agent(task=task, llm=llm)
    await agent.run()

if __name__ == '__main__':
    asyncio.run(main())