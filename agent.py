#!/usr/bin/env python3
"""
DeepSeek Browser-Use Agent Example

This script demonstrates how to create a browser-use agent using the DeepSeek model
and connect to your current browser using Chrome DevTools Protocol (CDP).
"""

import asyncio
import os
from dotenv import load_dotenv
from browser_use import Agent, Controller
from browser_use.browser import Browser<PERSON>rofile, BrowserSession
from browser_use.llm import ChatDeepSeek

# Load environment variables from .env file
load_dotenv()

# Custom system message with important rules
extend_system_message = """
Remember the most important rules: 
1. When performing a search task, open https://www.google.com/ first for search. 
2. Provide a clear final output.
3. Be concise and accurate in your responses.
"""

async def main():
    # Get DeepSeek API key from environment variables
    deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
    if deepseek_api_key is None:
        print('DEEPSEEK_API_KEY not found in environment variables.')
        print('Please set it in your .env file:')
        print('DEEPSEEK_API_KEY=your_actual_api_key_here')
        return

    # Initialize the DeepSeek LLM with custom configuration
    llm = ChatDeepSeek(
        base_url='https://api.deepseek.com/v1',  # DeepSeek API endpoint
        model='deepseek-chat',                   # Model name
        api_key=deepseek_api_key,                # API key from environment
        temperature=0.7,                         # Creativity parameter
    )

    # Create browser session to connect to current browser via CDP
    # Make sure your browser is running with --remote-debugging-port=9222
    browser_session = BrowserSession(
        browser_profile=BrowserProfile(
            headless=False,  # Show browser window
        ),
        cdp_url='http://localhost:9222',  # Default CDP URL
    )
    
    # Create controller for the agent
    controller = Controller()

    # Create the agent with a specific task and connect to current browser
    agent = Agent(
        task='Find the current weather in Beijing and compare it with Shanghai',
        llm=llm,
        use_vision=False,                        # Disable vision capabilities
        message_context=extend_system_message,   # Custom system message
        max_steps=50,                            # Maximum number of steps
        controller=controller,                   # Controller for browser actions
        browser_session=browser_session,         # Connect to current browser
    )
    
    print("Starting DeepSeek browser-use agent...")
    print("Task: Find the current weather in Beijing and compare it with Shanghai")
    print("Connecting to current browser via CDP at http://localhost:9222")
    print("=" * 70)
    
    try:
        # Run the agent
        result = await agent.run()
        print("\nTask completed successfully!")
        print("Results:")
        print(result)
    except Exception as e:
        print(f"\nError occurred: {str(e)}")
    finally:
        # Close the browser session
        await browser_session.close()

# ALTERNATIVE DEEPSEEK CONFIGURATIONS
# ==============================
# You can adjust these parameters based on your needs:

# For different DeepSeek models:
# llm = ChatDeepSeek(
#     base_url='https://api.deepseek.com/v1',
#     model='deepseek-coder',  # For coding tasks
#     api_key=deepseek_api_key,
#     temperature=0.3,         # Lower temperature for more deterministic output
# )

# For custom endpoints (if using a proxy):
# llm = ChatDeepSeek(
#     base_url='https://your-deepseek-proxy.com/v1',
#     model='deepseek-chat',
#     api_key=deepseek_api_key,
# )

if __name__ == '__main__':
    asyncio.run(main())