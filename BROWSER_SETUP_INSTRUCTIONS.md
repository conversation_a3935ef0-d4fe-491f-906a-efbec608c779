# Browser Setup Instructions for CDP Connection

This guide explains how to set up your browser to work with the browser-use agent via Chrome DevTools Protocol (CDP).

## Prerequisites

1. Google Chrome, Microsoft Edge, or another Chromium-based browser
2. The browser-use Python package installed

## Setting Up Your Browser for CDP Connection

### Method 1: Using Chrome with Remote Debugging

1. **Close all Chrome instances** to avoid conflicts

2. **Start Chrome with remote debugging enabled**:
   
   **On Windows:**
   - Press `Win + R` to open the Run dialog
   - Type `chrome --remote-debugging-port=9222` and press Enter
   - Or create a shortcut and add `--remote-debugging-port=9222` to the target

   **On macOS:**
   ```bash
   /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222
   ```

   **On Linux:**
   ```bash
   google-chrome --remote-debugging-port=9222
   ```

3. **Verify the connection**:
   - Open your browser and navigate to `http://localhost:9222/json/version`
   - You should see a JSON response with browser information

### Method 2: Using Chrome with Custom Profile

If you want to use a separate profile for the agent:

1. **Create a new Chrome shortcut**
2. **Add these arguments to the shortcut target**:
   ```
   --remote-debugging-port=9222 --user-data-dir="C:\path\to\custom\profile"
   ```

### Method 3: Using Edge Browser

For Microsoft Edge, use similar commands:

```bash
# Windows
msedge --remote-debugging-port=9222

# macOS
/Applications/Microsoft\ Edge.app/Contents/MacOS/Microsoft\ Edge --remote-debugging-port=9222
```

## Running the Agent

Once your browser is running with remote debugging enabled:

1. **Make sure your browser is open** at `http://localhost:9222`
2. **Run the agent**:
   ```bash
   python Agent.py
   ```

The agent will connect to your existing browser instance and control it to perform the specified task.

## Troubleshooting

### Common Issues:

1. **"Connection refused" error**:
   - Make sure Chrome is running with `--remote-debugging-port=9222`
   - Check that no other process is using port 9222

2. **Browser doesn't open**:
   - Ensure all Chrome instances are closed before starting with the flag
   - Try using a custom user data directory

3. **Agent doesn't connect**:
   - Verify the CDP URL in your code matches the port you're using
   - Check `http://localhost:9222/json/version` in your browser

### Port Conflicts:

If port 9222 is already in use, you can use a different port:

```python
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        headless=False,
    ),
    cdp_url='http://localhost:9223',  # Use a different port
)
```

And start Chrome with:
```
chrome --remote-debugging-port=9223
```

## Security Considerations

- Remote debugging allows external control of your browser
- Only use this in a trusted environment
- Close the browser when you're done with the agent
- Consider using a separate browser profile for automation tasks

## Advanced Configuration

You can customize the browser session further:

```python
browser_session = BrowserSession(
    browser_profile=BrowserProfile(
        headless=False,
        window_width=1920,
        window_height=1080,
        user_data_dir="path/to/custom/profile",  # Use specific profile
    ),
    cdp_url='http://localhost:9222',
)
```

This setup allows the browser-use agent to control your existing browser instance rather than launching a new one.